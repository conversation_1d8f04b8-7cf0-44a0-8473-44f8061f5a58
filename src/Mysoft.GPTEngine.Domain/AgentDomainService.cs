using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using SqlSugar.Extensions;
using ParamValueDto = Mysoft.GPTEngine.SemanticKernel.Core.Dtos.ParamValueDto;


namespace Mysoft.GPTEngine.Domain
{
    public class AgentDomainService : DomainServiceBase
    {
        private readonly ChatRepostory _chatRepostory;
        private readonly ChatArgumentsRepostory _chatArgumentsRepostory;
        private readonly ChatMessageNodeLogEntityRepostory _chatMessageNodeLogEntityRepostory;
        private readonly ChatMessageKnowledgeNodeLogEntityRepostory _chatMessageKnowledgeNodeLogEntityRepostory;
        private readonly ChatMessageRepostory _chatMessageRepostory;
        private readonly ChatMessageFileRepostory _chatMessageFileRepostory;
        private readonly PublishedSkillRepostory _publishedSkillRepostory;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        private readonly ModelRepostory _modelRepostory;
        private readonly SkillRepostory _skillRepostory;
        private readonly PromptTemplateRepostory _promptTemplateRepostory;
        private readonly PromptRepostory _promptRepostory;
        private readonly PromptParamRepostory _promptParamRepostory;
        private readonly ILogger<AgentDomainService> _logger;
        private readonly IMysoftApiService _mysoftApiService;

        public AgentDomainService(ChatRepostory chatRepostory
            , ChatArgumentsRepostory chatArgumentsRepostory
            , ChatMessageRepostory chatMessageRepostory
            , ChatMessageFileRepostory chatMessageFileRepostory
            , ChatMessageNodeLogEntityRepostory chatMessageNodeLogEntityRepostory
            , ChatMessageKnowledgeNodeLogEntityRepostory chatMessageKnowledgeNodeLogEntityRepostory
            , PublishedSkillRepostory publishedSkillRepostory
            , SkillRepostory skillRepostory
            , ModelRepostory modelRepostory
            , ModelInstanceRepostory modelInstanceRepostory
            , PromptRepostory promptRepostory
            , PromptTemplateRepostory promptTemplateRepostory
            , PromptParamRepostory promptParamRepostory
            , IHttpContextAccessor httpContextAccessor
            , IMysoftApiService mysoftApiService
            , IMysoftContextFactory mysoftContextFactory, IMapper mapper
            , ILogger<AgentDomainService> logger) : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _chatRepostory = chatRepostory;
            _chatArgumentsRepostory = chatArgumentsRepostory;
            _chatMessageNodeLogEntityRepostory = chatMessageNodeLogEntityRepostory;
            _chatMessageKnowledgeNodeLogEntityRepostory = chatMessageKnowledgeNodeLogEntityRepostory;
            _chatMessageRepostory = chatMessageRepostory;
            _chatMessageFileRepostory = chatMessageFileRepostory;
            _publishedSkillRepostory = publishedSkillRepostory;
            _modelInstanceRepostory = modelInstanceRepostory;
            _modelRepostory = modelRepostory;
            _skillRepostory = skillRepostory;
            _promptTemplateRepostory = promptTemplateRepostory;
            _logger = logger;
            _promptRepostory = promptRepostory;
            _promptParamRepostory = promptParamRepostory;
            _mysoftApiService = mysoftApiService;
        }

        public async Task<ChatRunDto> StreamingChatCompletionProcessAsync(ChatInputDto chatInput)
        {
            var chatRun = new ChatRunDto();
            chatRun.Stopwatch.Start();
            chatRun.SkillGUID = chatInput.SkillGUID;
            //更新技能元数据
            var publishedSkill = await _publishedSkillRepostory.GetAsync(x => x.SkillGUID == chatInput.SkillGUID).ConfigureAwait(false);
            Verify.NotNull(publishedSkill);
            Verify.NotNullOrWhiteSpace(publishedSkill.Metadata);

            // 添加调试日志查看原始JSON
            _logger.LogInformation("[StreamingChatCompletionProcessAsync] 原始Metadata JSON: {metadata}", publishedSkill.Metadata);

            var skillMetadata = JsonConvert.DeserializeObject<SkillMetadataDto>(publishedSkill.Metadata);

            // 添加调试日志查看反序列化结果
            if (skillMetadata?.Agent != null)
            {
                _logger.LogInformation("[StreamingChatCompletionProcessAsync] 反序列化后Agent.Mcps状态: {mcpsIsNull}, 数量: {count}",
                    skillMetadata.Agent.Mcps == null ? "null" : "not null",
                    skillMetadata.Agent.Mcps?.Count ?? 0);

                if (skillMetadata.Agent.Mcps != null)
                {
                    _logger.LogInformation("[StreamingChatCompletionProcessAsync] Agent.Mcps详细信息: {@mcps}", skillMetadata.Agent.Mcps);
                }
            }
            if (SkillModeConstant.Flow.Equals(skillMetadata.Mode))
            {
                if (MysoftConstant.MetadataLatestVersion != skillMetadata.MetaDataVersion)
                {
                    await upgradeSkill(chatInput.SkillGUID.ToString());
                    publishedSkill = await _publishedSkillRepostory.GetAsync(x => x.SkillGUID == chatInput.SkillGUID).ConfigureAwait(false);
                    skillMetadata = JsonConvert.DeserializeObject<SkillMetadataDto>(publishedSkill.Metadata);
                }
                await CheckEvalPrompt(chatInput, skillMetadata);
                // 编排中存在提示词则去引用中查找
                //TODO：提示词的初始化逻辑需要跳转
                if (skillMetadata.Orchestrations.Any(x => x.Prompts != null && x.Prompts.Any()))
                {
                    var promptGuids = skillMetadata.Orchestrations.Where(x => x.Prompts != null).SelectMany(x => x.Prompts.Select(p => p.Id)).Distinct().ToList();
                    _logger.LogInformation("提示词模版GUIDS：{0}", promptGuids);

                    var prompts = await _promptRepostory.GetListAsync(x => promptGuids.Contains(x.PromptGUID)).ConfigureAwait(false);
                    var promptTemplates = await _promptTemplateRepostory.GetListAsync(x => promptGuids.Contains(x.PromptGUID)).ConfigureAwait(false);
                    if (promptTemplates.Any())
                    {
                        var promptDic = prompts.ToDictionary(x => x.PromptGUID, x => x);
                        var promptTemplateDic = promptTemplates.ToDictionary(x => x.PromptGUID, x => x);
                        List<string> modelInstanceCodeList = prompts.Select(obj => obj.ModelInstanceCode).Where(s => !string.IsNullOrEmpty(s)).Distinct().ToList();

                        var modelInstanceDic = (await _modelInstanceRepostory.GetListAsync(x => modelInstanceCodeList.Contains(x.InstanceCode)).ConfigureAwait(false))
                            .ToDictionary(x => x.InstanceCode, x => x.InstanceGUID);

                        foreach (var item in skillMetadata.Orchestrations)
                        {
                            if (item.Prompts != null)
                            {
                                foreach (var prompt in item.Prompts)
                                {
                                    if (promptDic.ContainsKey(prompt.Id) == false || promptTemplateDic.ContainsKey(prompt.Id) == false) continue;
                                    var promptEntiy = promptDic[prompt.Id];
                                    if (promptEntiy.Mode == 1 && !String.IsNullOrEmpty(promptTemplateDic[prompt.Id].MessageContent))
                                    {
                                        prompt.MessageContents = JsonConvert.DeserializeObject<List<MessageContent>>(promptTemplateDic[prompt.Id].MessageContent).OrderBy(x => x.Index).ToList();
                                    }
                                    prompt.PromptTemplate = promptTemplateDic[prompt.Id].PromptTemplate;
                                    prompt.OutputType = promptEntiy.OutputType;
                                    prompt.ExecutionSetting = promptEntiy.ExecutionSetting;
                                    prompt.ModelInstanceCode = promptEntiy.ModelInstanceCode;
                                    prompt.ModelInstanceGUID = (string.IsNullOrEmpty(promptEntiy.ModelInstanceCode) == false && modelInstanceDic.ContainsKey(promptEntiy.ModelInstanceCode))
                                        ? (Guid?)modelInstanceDic[promptEntiy.ModelInstanceCode]
                                        : null;
                                }
                            }

                        }
                    }
                }
            }
            

            if (await _chatRepostory.IsAnyAsync(x => x.ChatGUID == chatInput.ChatGUID) == false)
                await AddChatCompletionProcessAsync(chatInput, chatRun, skillMetadata);
            else
                await UpdateChatCompletionProcessAsync(chatInput, chatRun, skillMetadata);
            //开启多轮会话的提示词节点添加对应节点的chatMessageNodeLog到上下文
            if (SkillModeConstant.Flow.Equals(skillMetadata.Mode))
            {
                _ = AddPromptChatMessageNodeLog(chatInput, chatRun, skillMetadata);
                AddKnowledgeChatMessageNodeLog(chatInput, chatRun, skillMetadata);
            }
            //图片文件保存
            await KernelArgumentsProcess(chatInput.Arguments, chatRun, chatInput.Input);

            if (string.IsNullOrEmpty(skillMetadata.ModelInstanceCode) == false)
            {
                chatRun.SkillModelCode = skillMetadata.ModelInstanceCode;
                var modelInstances = await _modelInstanceRepostory.GetFirstAsync(x => x.InstanceCode == skillMetadata.ModelInstanceCode);

                // 添加调试日志
                _logger.LogInformation("[StreamingChatCompletionProcessAsync] 查询到的ModelInstance实体 - InstanceCode: {instanceCode}, SupportDeepThink: {supportDeepThink}, IsSupportTool: {isSupportTool}, CustomModelCode: {customModelCode}",
                    modelInstances?.InstanceCode, modelInstances?.SupportDeepThink, modelInstances?.IsSupportTool, modelInstances?.CustomModelCode);

                if (modelInstances != null)
                {
                    // 查询关联的ModelEntity来获取正确的ModelCode
                    var modelEntity = await _modelRepostory.GetFirstAsync(x => x.ModelGUID == modelInstances.ModelGUID);

                    chatRun.ModelInstance = _mapper.Map<ModelInstanceDto>(modelInstances);

                    // 解密API Key
                    if (!string.IsNullOrEmpty(chatRun.ModelInstance.ApiKey))
                    {
                        chatRun.ModelInstance.ApiKey = AesHelper.Decrypt(chatRun.ModelInstance.ApiKey);
                    }

                    // 修复ModelCode赋值逻辑：优先使用CustomModelCode，如果为空则使用ModelEntity的ModelCode
                    if (modelEntity != null)
                    {
                        chatRun.ModelInstance.ModelCode = !string.IsNullOrEmpty(modelInstances.CustomModelCode) ?
                            modelInstances.CustomModelCode : modelEntity.ModelCode;
                        chatRun.ModelInstance.ModelType = modelEntity.ModelType;
                        chatRun.ModelInstance.ServiceTypeEnum = modelEntity.ServiceTypeEnum;
                    }

                    // 添加修复后的调试日志
                    _logger.LogInformation("[StreamingChatCompletionProcessAsync] 修复后的ModelInstance DTO - InstanceCode: {instanceCode}, ModelCode: {modelCode}, CustomModelCode: {customModelCode}, SupportDeepThink: {supportDeepThink}, IsSupportTool: {isSupportTool}",
                        chatRun.ModelInstance?.InstanceCode, chatRun.ModelInstance?.ModelCode, chatRun.ModelInstance?.CustomModelCode, chatRun.ModelInstance?.SupportDeepThink, chatRun.ModelInstance?.IsSupportTool);
                }
            }
            else
            {
                var defaultModelInstances = await _modelInstanceRepostory.GetListAsync(x => x.IsDefault);
                if (defaultModelInstances == null || defaultModelInstances.Count == 0)
                {
                    return chatRun;
                }
                var modeGuids = defaultModelInstances.Select(x => x.ModelGUID).ToList();
                var model = await _modelRepostory.GetFirstAsync(x => modeGuids.Contains(x.ModelGUID) && x.ServiceTypeEnum == Shared.Enums.ServiceTypeEnum.TextGeneration);
                if (model == null)
                {
                    return chatRun;
                }

                var selectedModelInstance = defaultModelInstances.First(x => x.ModelGUID == model.ModelGUID);

                // 添加调试日志
                _logger.LogInformation("[StreamingChatCompletionProcessAsync] 查询到的默认ModelInstance实体 - InstanceCode: {instanceCode}, SupportDeepThink: {supportDeepThink}, IsSupportTool: {isSupportTool}, CustomModelCode: {customModelCode}",
                    selectedModelInstance?.InstanceCode, selectedModelInstance?.SupportDeepThink, selectedModelInstance?.IsSupportTool, selectedModelInstance?.CustomModelCode);

                chatRun.ModelInstance = _mapper.Map<ModelInstanceDto>(selectedModelInstance);

                // 解密API Key
                if (!string.IsNullOrEmpty(chatRun.ModelInstance.ApiKey))
                {
                    chatRun.ModelInstance.ApiKey = AesHelper.Decrypt(chatRun.ModelInstance.ApiKey);
                }

                // 修复默认模型的ModelCode赋值逻辑：优先使用CustomModelCode，如果为空则使用ModelEntity的ModelCode
                chatRun.ModelInstance.ModelCode = !string.IsNullOrEmpty(selectedModelInstance.CustomModelCode) ?
                    selectedModelInstance.CustomModelCode : model.ModelCode;
                chatRun.ModelInstance.ModelType = model.ModelType;
                chatRun.ModelInstance.ServiceTypeEnum = model.ServiceTypeEnum;

                // 添加修复后的调试日志
                _logger.LogInformation("[StreamingChatCompletionProcessAsync] 修复后的默认ModelInstance DTO - InstanceCode: {instanceCode}, ModelCode: {modelCode}, CustomModelCode: {customModelCode}, SupportDeepThink: {supportDeepThink}, IsSupportTool: {isSupportTool}",
                    chatRun.ModelInstance?.InstanceCode, chatRun.ModelInstance?.ModelCode, chatRun.ModelInstance?.CustomModelCode, chatRun.ModelInstance?.SupportDeepThink, chatRun.ModelInstance?.IsSupportTool);
            }


            return chatRun;
        }


        private async Task CheckEvalPrompt(ChatInputDto chatInput, SkillMetadataDto skillMetadataDto)
        {
            if (!(chatInput.Arguments.Any(x => x.Key == "System_Eval_Prompt_GUID") && skillMetadataDto.Code == "system_eval_prompt") || skillMetadataDto.Flow.Nodes.Count != 3)
            {
                return;
            }

            PromptDto promptDto = skillMetadataDto.Orchestrations?.FirstOrDefault(x => x.Prompts != null)?.Prompts.FirstOrDefault();
            if (promptDto == null)
            {
                return;
            }

            string newId = chatInput.Arguments.FirstOrDefault(x => x.Key == "System_Eval_Prompt_GUID")?.Value;
            if (string.IsNullOrEmpty(newId))
            {
                return;
            }
            promptDto.Id = Guid.Parse(newId);

            var flowNode = skillMetadataDto.Flow.Nodes[1];
            var flowNodeStart = skillMetadataDto.Flow.Nodes[0];
            flowNode.Config.Id = promptDto.Id.ToString();
            flowNode.Config.TemplateId = promptDto.Id.ToString();
            var promptParams = await _promptParamRepostory.GetListAsync(x => x.PromptGUID == promptDto.Id && x.ParamType == ParamTypeEnum.Input);
            flowNode.Config.Inputs = new List<ParamDto>();
            foreach (var param in promptParams)
            {
                flowNode.Config.Inputs.Add(new ParamDto()
                {
                    Code = param.ParamCode,
                    Name = param.ParamName,
                    Type = param.FiledType,
                    Required = param.IsRequired,
                    Value = new ParamValueDto()
                    {
                        Type = "ref",
                        Content = $"NodeOutput_start0_{param.ParamCode}"
                    }
                });
                flowNodeStart.Config.Outputs.Add(new ParamDto()
                {
                    Code = param.ParamCode,
                    Name = param.ParamName,
                    Type = param.FiledType,
                    Required = param.IsRequired
                });
            }
        }

        /// <summary>
        /// 硬编码添加DataQueryActivity节点用于测试
        /// </summary>
        private void AddDataQueryActivityNodeForTesting(SkillMetadataDto skillMetadata)
        {
            try
            {
                // 查找start0节点
                var start0Node = skillMetadata.Flow.Nodes.FirstOrDefault(n => n.Code == "start0");
                if (start0Node == null)
                {
                    _logger.LogWarning("[AddDataQueryActivityNodeForTesting] 未找到start0节点，跳过添加DataQueryActivity节点");
                    return;
                }

                // 创建DataQueryActivity节点
                var dataQueryNode = new FlowNode
                {
                    Id = Guid.NewGuid().ToString(),
                    Code = "dataquery1",
                    Type = SkillNodeTypeConstant.DataQuery,
                    Name = "数据查询测试",
                    Description = "测试DataQueryActivity功能",
                    Config = new NodeConfig
                    {
                        Inputs = new List<ParamDto>
                        {
                            new ParamDto
                            {
                                Code = "input",
                                Name = "用户输入",
                                Type = "string",
                                Value = new ParamValueDto
                                {
                                    Type = "ref",
                                    Content = "NodeOutput_start0_input"
                                }
                            }
                        },
                        Outputs = new List<ParamDto>
                        {
                            new ParamDto
                            {
                                Code = "output",
                                Name = "查询结果",
                                Type = "string"
                            }
                        },
                        Knowledges = new List<string>
                        {
                            "kn_system_my_knowledge"  // 硬编码一个测试知识库
                        }
                    }
                };

                // 确保start0节点有output参数
                if (start0Node.Config.Outputs == null)
                {
                    start0Node.Config.Outputs = new List<ParamDto>();
                }

                // 如果start0节点没有input输出参数，添加一个
                if (!start0Node.Config.Outputs.Any(o => o.Code == "input"))
                {
                    start0Node.Config.Outputs.Add(new ParamDto
                    {
                        Code = "input",
                        Name = "用户输入",
                        Type = "string"
                    });
                }

                // 将DataQueryActivity节点插入到start0节点后面
                var start0Index = skillMetadata.Flow.Nodes.IndexOf(start0Node);
                skillMetadata.Flow.Nodes.Insert(start0Index + 1, dataQueryNode);

                // 修改OrchestrationTemplate以包含DataQueryActivity节点的调用
                ModifyOrchestrationTemplateForDataQuery(skillMetadata, dataQueryNode.Id);

                _logger.LogInformation("[AddDataQueryActivityNodeForTesting] 成功添加DataQueryActivity节点: {nodeId}, 位置: {position}",
                    dataQueryNode.Id, start0Index + 1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddDataQueryActivityNodeForTesting] 添加DataQueryActivity节点时发生异常");
            }
        }

        /// <summary>
        /// 修改OrchestrationTemplate以包含DataQueryActivity节点的调用
        /// </summary>
        private void ModifyOrchestrationTemplateForDataQuery(SkillMetadataDto skillMetadata, string dataQueryNodeId)
        {
            try
            {
                var orchestration = skillMetadata.Orchestrations.FirstOrDefault();
                if (orchestration == null)
                {
                    _logger.LogWarning("[ModifyOrchestrationTemplateForDataQuery] 未找到OrchestrationTemplate");
                    return;
                }

                var originalTemplate = orchestration.OrchestrationTemplate;
                _logger.LogInformation("[ModifyOrchestrationTemplateForDataQuery] 原始模板: {template}", originalTemplate);

                // 在原始模板的基础上插入DataQueryActivity，保持原有的执行机制
                var newTemplate = originalTemplate.Replace(
                    "{{!-- Step 2: 结束卡片 --}}",
                    $@"{{!-- Step 2: 数据查询测试 --}}
{{{{set ""System_CurrentNodeGUID"" ""{dataQueryNodeId}""}}}}
{{{{DataQueryActivity-DataQueryActivity}}}}
{{!-- Step 3: 结束卡片 --}}"
                );

                orchestration.OrchestrationTemplate = newTemplate;
                _logger.LogInformation("[ModifyOrchestrationTemplateForDataQuery] 修改后的模板: {template}", newTemplate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ModifyOrchestrationTemplateForDataQuery] 修改OrchestrationTemplate时发生异常");
            }
        }

        public async Task<List<ChatMessageNodeLogEntity>> GetChatMessageLog(Guid messageGuid)
        {
            return await _chatMessageNodeLogEntityRepostory.GetListAsync(x => x.BatchGUID == messageGuid);
        }
        public async Task<List<ChatMessageEntity>> GetChatMessageList(Guid chatGuid)
        {
            return await _chatMessageRepostory.GetListAsync(x => x.ChatGUID == chatGuid);
        }
        public async Task<Guid> CreateChat(CreateChatInputDto createChatInputDto)
        {
            _logger.LogInformation("开始创建会话");
            var chatRun = new ChatRunDto() { Chat = new ChatDto { ChatGUID = Guid.NewGuid() } };
            var publishedSkill = await _publishedSkillRepostory.GetAsync(x => x.SkillGUID == createChatInputDto.SkillGUID);

            Verify.NotNull(publishedSkill);
            Verify.NotNullOrWhiteSpace(publishedSkill.Metadata);

            foreach (var arg in createChatInputDto.Arguments)
            {
                arg.Key = $"NodeOutput_start0_{arg.Key}";
            }

            await KernelArgumentsProcess(createChatInputDto.Arguments, chatRun, null);

            var chatArguments = new List<ChatArgumentsEntity>();
            foreach (var argument in chatRun.ChatArguments)
            {
                if (argument.Value is string)
                {
                    chatArguments.Add(new ChatArgumentsEntity { ChatArgumentsGUID = Guid.NewGuid(), ChatGUID = chatRun.Chat.ChatGUID, Key = argument.Key, Value = (string)argument.Value });
                }
            }

            // await _chatArgumentsRepostory.DeleteAsync(x => x.ChatGUID == chatRun.Chat.ChatGUID);
            await _chatArgumentsRepostory.InsertManyAsync(chatArguments);
            _logger.LogInformation("会话已经创建，会话ID：{0}", chatRun.Chat.ChatGUID);
            return await Task.FromResult(chatRun.Chat.ChatGUID);
        }

        private Task<Dictionary<string, object>> GetAccessTokenUserInfo()
        {
            var accessTokenContext = _mysoftContextFactory.GetAccessTokenContext();
            var dic = BeanToDictionaryConverter.ConvertBeanToDictionary(accessTokenContext, KernelArgumentsConstant._prefixKeyword + KernelArgumentsConstant._separator);
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
            // 未开启用户鉴权，不使用access_token中数据，使用系统参数
            if (!userContext.EnableApplicationUserAuthorization)
            {
                return Task.FromResult<Dictionary<string, object>>(null);
            }

            return Task.FromResult(dic);
        }
        
        private async Task AddChatCompletionProcessAsync(ChatInputDto chatInput, ChatRunDto chatRun, SkillMetadataDto skillMetadata)
        {
            var dic = await GetAccessTokenUserInfo();
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
            var userGuid = GetArgumentsValue(chatInput.Arguments, KernelArgumentsConstant.SystemKeywordUserGUID, userContext.UserId.ToString(), dic);
            var customerId = GetArgumentsValue(chatInput.Arguments, KernelArgumentsConstant.SystemKeywordCustomerGUID, "00000000-0000-0000-0000-000000000000", dic);
            var chat = new ChatEntity
            {
                ChatGUID = chatInput.ChatGUID,
                ApplicationGUID = chatInput.ApplicationGUID??Guid.Empty,
                AssistantGUID = chatInput.AssistanGUID,
                SkillGUID = chatInput.SkillGUID,
                Title = chatInput.Input,
                UserGUID = userGuid,
                UserName = GetArgumentsValue(chatInput.Arguments, KernelArgumentsConstant.SystemKeywordUserName, userContext.UserName, dic),
                CustomerId = customerId,
                CustomerName = GetArgumentsValue(chatInput.Arguments, KernelArgumentsConstant.SystemKeywordCustomerName, null, dic),
                TenantCode = GetArgumentsValue(chatInput.Arguments, KernelArgumentsConstant.SystemKeywordTenantCode, null, dic),
                TenantName = GetArgumentsValue(chatInput.Arguments, KernelArgumentsConstant.SystemKeywordTenantName, null, dic)
            };
            AddDocumentsToChatRun(chatInput.Documents, chatRun, "DocumentAnalysis");
            AddDocumentsToChatRun(chatInput.Images, chatRun, "ImageAnalysis");
            await _chatRepostory.InsertAsync(chat);
            chatRun.Chat = _mapper.Map<ChatDto>(chat);
            chatRun.ChatArguments = await ChatCompletionChatArgumentsProcessAsync(chatInput);
            chatRun.ChatMessages = new List<ChatMessageDto>();
            chatRun.Mode = skillMetadata.Mode;
            if (SkillModeConstant.Flow.Equals(skillMetadata.Mode))
            {
                chatRun.SkillOrchestration = skillMetadata.Orchestrations.FirstOrDefault();

                // 硬编码添加DataQueryActivity节点用于测试
                AddDataQueryActivityNodeForTesting(skillMetadata);

                chatRun.Nodes = skillMetadata.Flow.Nodes;
                chatRun.Next = chatInput.Next;
                chatRun.NextId = chatInput.NextId;
            } else
            {
                chatRun.Agent = skillMetadata.Agent;
            }
        }
        private async Task UpdateChatCompletionProcessAsync(ChatInputDto chatInput, ChatRunDto chatRun, SkillMetadataDto skillMetadata)
        {
            chatRun.Chat = _mapper.Map<ChatDto>(await _chatRepostory.GetAsync(x => x.ChatGUID == chatInput.ChatGUID));
            chatRun.ChatArguments = await ChatCompletionChatArgumentsProcessAsync(chatInput);
            chatRun.ChatMessages = _mapper.Map<List<ChatMessageDto>>(await _chatMessageRepostory.GetListAsync(x => x.ChatGUID == chatInput.ChatGUID));
            AddDocumentsToChatRun(chatInput.Documents, chatRun, SkillNodeTypeConstant.DocumentAnalysisNode);
            AddDocumentsToChatRun(chatInput.Images, chatRun, SkillNodeTypeConstant.ImageAnalysisNode);
            chatRun.Mode = skillMetadata.Mode;
            if (SkillModeConstant.Flow.Equals(skillMetadata.Mode))
            {
                chatRun.SkillOrchestration = skillMetadata.Orchestrations.FirstOrDefault();

                // 硬编码添加DataQueryActivity节点用于测试
                AddDataQueryActivityNodeForTesting(skillMetadata);

                chatRun.Nodes = skillMetadata.Flow.Nodes;
                chatRun.Next = chatInput.Next;
                chatRun.NextId = chatInput.NextId;
            } else
            {
                chatRun.Agent = skillMetadata.Agent;
            }
            chatRun.SaveLog = chatInput.SaveLog??true;
        }
        private async Task AddPromptChatMessageNodeLog(ChatInputDto chatInput, ChatRunDto chatRun, SkillMetadataDto skillMetadata)
        {
            var promptTemplateFlowNodes = chatRun.Nodes.FindAll(x => (x.Type == SkillNodeTypeConstant.PromptTemplateNode
                                                                      || x.Type == SkillNodeTypeConstant.Classification) && x.Config.Memories > 0);
            if (promptTemplateFlowNodes.Count == 0)
            {
                return;
            }
            foreach (var node in promptTemplateFlowNodes)
            {
                var chatMessageNodeLogDtos = _mapper.Map<List<ChatMessageNodeLogDto>>(
                    await _chatMessageNodeLogEntityRepostory.GetListAsync(x => x.NodeGUID.ToString() == node.Id && x.ChatGUID == chatRun.Chat.ChatGUID));
                if (chatMessageNodeLogDtos.Count > 0) chatRun.NodeLogs.AddRange(chatMessageNodeLogDtos);
            }
        }
        private async Task AddKnowledgeChatMessageNodeLog(ChatInputDto chatInput, ChatRunDto chatRun, SkillMetadataDto skillMetadata)
        {
            var knowledgeFlowNodes = chatRun.Nodes.FindAll(x => x.Type == SkillNodeTypeConstant.KnowledgeNode && x.Config.IsCOR);
            if (knowledgeFlowNodes.Count == 0)
            {
                return;
            }
            foreach (var node in knowledgeFlowNodes)
            {
                var chatMessageNodeLogDtos = _mapper.Map<List<ChatMessageNodeLogDto>>(
                    await _chatMessageNodeLogEntityRepostory.GetListAsync(x => x.NodeGUID.ToString() == node.Id && x.ChatGUID == chatRun.Chat.ChatGUID));
                if (chatMessageNodeLogDtos.Count > 0) chatRun.NodeLogs.AddRange(chatMessageNodeLogDtos);
            }
        }
        private async Task<KernelArguments> ChatCompletionChatArgumentsProcessAsync(ChatInputDto chatInput)
        {
            var argumnts = chatInput.Next==false ? new List<ChatArgumentsEntity>() : await _chatArgumentsRepostory.GetListAsync(x => x.ChatGUID == chatInput.ChatGUID);
            var kernelArguments = new KernelArguments();
            if (argumnts?.Any() == true)
            {
                foreach (var arg in argumnts)
                {
                    if (!kernelArguments.ContainsName(arg.Key))
                    {
                        kernelArguments.Add(arg.Key, arg.Value);
                    }
                    else
                    {
                        kernelArguments[arg.Key] = arg.Value;
                    }
                }
            }
            //添加会话文件到系统会话变量 
            if (chatInput.Documents != null && chatInput.Documents.Count > 0)
            {
                KernelArgumentsHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentDocument, string.Join(",", chatInput.Documents.Select(item => item.Id)));
                KernelArgumentsHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentDocumentName, string.Join(",", chatInput.Documents.Select(item => item.Name)));
                KernelArgumentsHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentDocumentSize, string.Join(",", chatInput.Documents.Select(item => item.Size)));
            }
            else if(!chatInput.Next)
            {
                KernelArgumentsRemoveHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentDocument);
                KernelArgumentsRemoveHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentDocumentName);
                KernelArgumentsRemoveHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentDocumentSize);
            }

            if (chatInput.Images != null && chatInput.Images.Count > 0)
            {
                KernelArgumentsHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentImage, string.Join(",", chatInput.Images.Select(item => item.Id)));
                KernelArgumentsHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentImageName, string.Join(",", chatInput.Images.Select(item => item.Name)));
                KernelArgumentsHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentImageSize, string.Join(",", chatInput.Images.Select(item => item.Size)));
            }
            else if (!chatInput.Next)
            {
                KernelArgumentsRemoveHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentImage);
                KernelArgumentsRemoveHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentImageName);
                KernelArgumentsRemoveHandle(kernelArguments, KernelArgumentsConstant.SystemKeywordCurrentImageSize);
            }

            return await Task.FromResult(kernelArguments);
        }
        private void KernelArgumentsHandle(KernelArguments kernelArguments, string key, string value)
        {
            if (!kernelArguments.ContainsName(key))
            {
                kernelArguments.Add(key, value);
            }
            else
            {
                kernelArguments[key] = value;
            }
        }
        private void KernelArgumentsRemoveHandle(KernelArguments kernelArguments, string key)
        {
            if (kernelArguments.ContainsName(key))
            {
                kernelArguments.Remove(key);
            }
        }
        private async Task KernelArgumentsProcess(List<KeyValueDto> arguments, ChatRunDto chatRun, string input = null)
        {
            var dic = await GetAccessTokenUserInfo();

            if (arguments?.Count() > 0)
            {
                foreach (var argument in arguments)
                {
                    if (chatRun.ChatArguments.ContainsName(argument.Key))
                    {
                        chatRun.ChatArguments[argument.Key] = argument.Value;
                        continue;
                    }
                    chatRun.ChatArguments.Add(argument.Key, argument.Value);
                }
            }

            if (dic != null && dic.Count > 0)
            {
                foreach (var kv in dic)
                {
                    if (kv.Value != null && !string.IsNullOrWhiteSpace(kv.Value.ToString()))
                    {
                        if (!chatRun.ChatArguments.ContainsName(kv.Key))
                        {
                            chatRun.ChatArguments.Add(kv.Key, kv.Value);
                        }
                        else
                        {
                            chatRun.ChatArguments[kv.Key] = kv.Value;
                        }
                    }
                }
            }


            if (input == null) return;
            if (chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.Input))
            {
                chatRun.ChatArguments[KernelArgumentsConstant.Input] = input;
            }
            else
            {
                chatRun.ChatArguments.Add(KernelArgumentsConstant.Input, input);
            }
            await Task.CompletedTask;
        }


        private string GetArgumentsValue(List<KeyValueDto> arguments, string key, string defaultValue, Dictionary<string, object> dic)
        {
            if (dic != null && dic.ContainsKey(key) && dic[key] != null && !string.IsNullOrWhiteSpace(dic[key].ToString()))
            {
                return dic[key].ToString();
            }

            var argValue = arguments.FirstOrDefault(x => x.Key == key)
                ?.Value;

            if (!string.IsNullOrWhiteSpace(argValue)) return argValue;

            return defaultValue;
        }

        public async Task SaveChatArguments()
        {
            var chatRun = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));

            if (!chatRun.SaveLog) return;

            var chat = _mapper.Map<ChatEntity>(chatRun.Chat);
            chat.ModifiedTime = TimeZoneUtility.LocalNow();
            var chatMessageEntity = _mapper.Map<List<ChatMessageEntity>>(chatRun.ChatMessages);
            var chatMessageFileEntity = _mapper.Map<List<ChatMessageFileEntity>>(chatRun.ChatMessageFiles);
            var chatArguments = new List<ChatArgumentsEntity>();
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;

            var userGUID = !chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordUserGUID) ||
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordUserGUID] == null ||
                          String.IsNullOrEmpty(chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordUserGUID].ToString())
               ? userContext.UserId.ToString() : userContext.UserId.ToString();

            var userName = !chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordUserName) ||
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordUserName] == null ||
                           String.IsNullOrEmpty(chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordUserName].ToString())
                ? userContext.UserName.ToString() : chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordUserName].ToString();

            var customerId = !chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordCustomerGUID) ||
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordCustomerGUID] == null ||
                             String.IsNullOrEmpty(chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordCustomerGUID].ToString())
                ? Guid.Empty.ToString() : chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordCustomerGUID].ToString();

            var customerName = !chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordCustomerName) ||
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordCustomerName] == null ||
                               String.IsNullOrEmpty(chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordCustomerName].ToString())
                ? String.Empty : chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordCustomerName].ToString();

            var tenantCode = !chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordTenantCode) ||
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordTenantCode] == null ||
                             String.IsNullOrEmpty(chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordTenantCode].ToString()) ? String.Empty
                : chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordTenantCode].ToString();

            var tenantName = !chatRun.ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordTenantName) ||
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordTenantName] == null ||
                             String.IsNullOrEmpty(chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordTenantName].ToString()) ? String.Empty :
                chatRun.ChatArguments[KernelArgumentsConstant.SystemKeywordTenantName].ToString();

            foreach (var argument in chatRun.ChatArguments)
            {
                if (argument.Value is string)
                {
                    chatArguments.Add(new ChatArgumentsEntity
                    {
                        ChatArgumentsGUID = Guid.NewGuid(),
                        ChatGUID = chatRun.Chat.ChatGUID,
                        Key = argument.Key,
                        Value = (string)argument.Value,
                        UserGUID = userGUID,
                        UserName = userName,
                        CustomerId = customerId,
                        CustomerName = customerName
                    });
                }
            }

            var nodeLogs = _mapper.Map<List<ChatMessageNodeLogEntity>>(chatRun.NodeLogs);
            var knowledgeNodeLogs = _mapper.Map<List<ChatMessageKnowledgeNodeLogEntity>>(chatRun.KnowledgeNodeLogs);
            var chatMessagesNodeLogWithEmptyGuid = nodeLogs.Where(cm => cm.BatchGUID == Guid.Empty).ToList();
            foreach (var messageNodeLogEntity in chatMessagesNodeLogWithEmptyGuid)
            {
                messageNodeLogEntity.BatchGUID = chatRun.BatchGuid;
            }

            var chatMessagesWithEmptyGuid = chatMessageEntity.Where(cm => cm.ChatMessageGUID == Guid.Empty).ToList();
            String answer = String.Empty;
            foreach (var messageEntity in chatMessagesWithEmptyGuid)
            {
                messageEntity.BatchGUID =
                    messageEntity.BatchGUID.ToString().Equals("00000000-0000-0000-0000-000000000000")
                        ? chatRun.BatchGuid
                        : messageEntity.BatchGUID;
                messageEntity.UserGUID = userGUID;
                messageEntity.UserName = userName;
                messageEntity.CustomerId = customerId;
                messageEntity.CustomerName = customerName;
                messageEntity.TenantCode = tenantCode;
                messageEntity.TenantName = tenantName;
                messageEntity.ChatMessageGUID = Guid.NewGuid();
                messageEntity.CreatedTime = TimeZoneUtility.LocalNow();
                if (messageEntity.Role == ChatRoleConstant.User)
                {
                    foreach (var messageFileEntity in chatMessageFileEntity)
                    {
                        messageFileEntity.ChatMessageGUID = messageEntity.ChatMessageGUID;
                    }
                }
                if (messageEntity.Role == ChatRoleConstant.Assistant)
                {
                    answer = getAnswerValue(answer, messageEntity.Content, messageEntity.Error);
                }
            }
            var firstMessageWithEmptyGuid = chatMessagesWithEmptyGuid.FirstOrDefault(x => x.Role == ChatRoleConstant.User);
            if (firstMessageWithEmptyGuid != null)
            {
                firstMessageWithEmptyGuid.SetAnswer(answer.TrimEnd('；'));
            }
            _ = await _chatRepostory.UpdateAsync(chat);
            
            var existChatArguments = await _chatArgumentsRepostory.GetListAsync(
                x => x.ChatGUID == chatRun.Chat.ChatGUID);
            // 缓存 ChatArgumentsGUID 列表
            var existchatArgumentsGuids = existChatArguments.Select(x => x.ChatArgumentsGUID).ToList();
            if (chatArguments.Count > 0)
            {
                await _chatArgumentsRepostory.InsertManyAsync(chatArguments);
            }

            if (existchatArgumentsGuids.Count > 0)
            {
                await _chatArgumentsRepostory.DeleteAsync(x => existchatArgumentsGuids.Contains(x.ChatArgumentsGUID));
            }
            
            if (chatMessagesWithEmptyGuid.Count > 0)
            {
                await _chatMessageRepostory.InsertManyAsync(chatMessagesWithEmptyGuid);
            }
            if (chatMessageFileEntity.Count > 0)
            {
                await _chatMessageFileRepostory.InsertManyAsync(chatMessageFileEntity);
            }
            if (chatMessagesNodeLogWithEmptyGuid.Count > 0)
            {
                await _chatMessageNodeLogEntityRepostory.InsertManyAsync(chatMessagesNodeLogWithEmptyGuid);
            }
            if (knowledgeNodeLogs.Count > 0)
            {
                await _chatMessageKnowledgeNodeLogEntityRepostory.InsertManyAsync(knowledgeNodeLogs);
            }
            var skill = await _skillRepostory.GetAsync(x => x.SkillGUID == chatRun.Chat.SkillGUID);
            skill.SkillChatCount += 1;
            await _skillRepostory.UpdateAsyncWithoutModifiedField(skill);
        }

        private string getAnswerValue(string answer, string content, string error)
        {
            if (!String.IsNullOrEmpty(error))
            {
                return content;
            }
            int maxAnswerLength = 1024;
            if (answer.Length < maxAnswerLength)
            {
                char targetChar = ':';
                if (content.Length > 2 && content[1] == targetChar)
                {
                    if (content.StartsWith(EventDataConstant.TextEvent.Substring(0, 2)) || content.StartsWith(EventDataConstant.ErrorEvent.Substring(0, 2)))
                    {
                        return answer + content.Substring(2);
                    }
                    else if (content.StartsWith(EventDataConstant.DataEvent.Substring(0, 2)))
                    {
                        if (content.Contains(ChatMessageTypeConstant.FromCardContent))
                        {
                            return answer + "【交互卡片】；";
                        }
                        if (content.Contains(ChatMessageTypeConstant.MessageContent))
                        {
                            return answer + "【消息卡片】；";
                        }
                        else if (content.Contains(ChatMessageTypeConstant.FromTextCardContent))
                        {
                            return answer + "【文本生成卡片】；";
                        }
                        else if (content.Contains(ChatMessageTypeConstant.CardContent))
                        {
                            return answer + "【嵌入卡片】；";
                        }
                        else if (content.Contains(ChatMessageTypeConstant.FormBindingContent))
                        {
                            return answer + "【辅助录入】；";
                        }
                        else if (content.Contains(ChatMessageTypeConstant.PlanContent))
                        {
                            return answer + "【智能检查】；";
                        }
                        else if (content.Contains(ChatMessageTypeConstant.SourceContent))
                        {
                            return answer + "【知识库回答】；";
                        }
                    }
                }
                else
                {
                    return answer + content;
                }
            }
            return answer;
        }

        private void AddDocumentsToChatRun(List<ChatMessageFileInputDto> documents, ChatRunDto chatRun, string nodeType)
        {
            if (documents != null)
            {
                foreach (var document in documents)
                {
                    var chatMessageFile = new ChatMessageFileDto
                    {
                        ChatMessageFileGUID = Guid.NewGuid(),
                        FileId = document.Id,
                        FileName = document.Name,
                        PreviewUrl = document.PreviewUrl,
                        DownloadUrl = document.DownloadUrl,
                        Size = document.Size,
                        Status = document.Status,
                        Type = document.Type,
                        NodeType = nodeType
                    };
                    chatRun.ChatMessageFiles.Add(chatMessageFile);
                }
            }
        }

        private async Task<Boolean> upgradeSkill(string skillGUID)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            var uri = "/api/42000301/skill/upgradeSkill";
            _logger.LogInformation("技能元数据更新准备请求参数：路径：{0},参数:{1}", context.GptBuilderUrl + uri, skillGUID);
            var result = await _mysoftApiService.PostAsync(context.GptBuilderUrl + uri, skillGUID);
            _logger.LogInformation("技能元数据更新返回结果了：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);

            if (data?.Success == false)
            {
                return false;
            }
            if (data?.Data == null) return false;
            return data.Data.ObjToBool();
        }

        public async Task SaveChatState(Guid chatGUID, ChatStateEnum state, String? errorMessage = null)
        {
            var chatEntity = await _chatRepostory.GetAsync(x => x.ChatGUID == chatGUID);
            if (chatEntity != null)
            {
                chatEntity.State = (int)state;
                chatEntity.ErrorMessage = errorMessage;
                await _chatRepostory.UpdateAsync(chatEntity);
            }
        }
    }
}
