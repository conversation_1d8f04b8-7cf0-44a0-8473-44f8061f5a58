using System;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Plugin.Activities;
using Mysoft.GPTEngine.Plugin.Activities.Analysis;
using Mysoft.GPTEngine.Plugin.Activities.Api;
using Mysoft.GPTEngine.Plugin.Activities.Card;
using Mysoft.GPTEngine.Plugin.Activities.DataQuery;
using Mysoft.GPTEngine.Plugin.Activities.Knowledge;
using Mysoft.GPTEngine.Plugin.Activities.Llm;
using Mysoft.GPTEngine.Plugin.System;

namespace Mysoft.GPTEngine.Plugin
{
    public static class KernelExtension
    {
        public static void ImportSystemPlugin(this Kernel kernel, IServiceProvider serviceProvider)
        {
            if (kernel.Plugins.Count > 0) return;
            
            kernel.Plugins.AddFromType<CardPlugin>(nameof(CardPlugin), serviceProvider);
            kernel.Plugins.AddFromType<FormBindingPlugin>(nameof(FormBindingPlugin), serviceProvider);
            kernel.Plugins.AddFromType<ParameterPlugin>(nameof(ParameterPlugin), serviceProvider);
            kernel.Plugins.AddFromType<DocumentAnalysisPlugin>(nameof(DocumentAnalysisPlugin), serviceProvider);
            kernel.Plugins.AddFromType<ChatPlugin>(nameof(ChatPlugin), serviceProvider);
            kernel.Plugins.AddFromType<ImageChatPlugin>(nameof(ImageChatPlugin), serviceProvider);
            kernel.Plugins.AddFromType<KnowledgePlugin>(nameof(KnowledgePlugin), serviceProvider);
            kernel.Plugins.AddFromType<MysoftIPassApiActivity>(nameof(MysoftIPassApiActivity), serviceProvider);
            kernel.Plugins.AddFromType<ApiPlugin>(nameof(ApiPlugin), serviceProvider);
            kernel.Plugins.AddFromType<MysoftApiActivity>(nameof(MysoftApiActivity), serviceProvider);
            kernel.Plugins.AddFromType<ThirdPartyApiActivity>(nameof(ThirdPartyApiActivity), serviceProvider);
            kernel.Plugins.AddFromType<SelectorPlugin>(nameof(SelectorPlugin), serviceProvider);
            kernel.Plugins.AddFromType<PlanPlugin>(nameof(PlanPlugin), serviceProvider);


            kernel.Plugins.AddFromType<StartActivity>(nameof(StartActivity), serviceProvider);
            kernel.Plugins.AddFromType<EndActivity>(nameof(EndActivity), serviceProvider);
            kernel.Plugins.AddFromType<LlmActivity>(nameof(LlmActivity), serviceProvider);
            kernel.Plugins.AddFromType<MessageActivity>(nameof(MessageActivity), serviceProvider);
            kernel.Plugins.AddFromType<FormActivity>(nameof(FormActivity), serviceProvider);
            kernel.Plugins.AddFromType<TextCardActivity>(nameof(TextCardActivity), serviceProvider);
            kernel.Plugins.AddFromType<PageActivity>(nameof(PageActivity), serviceProvider);
            kernel.Plugins.AddFromType<KnowledgeActivity>(nameof(KnowledgeActivity), serviceProvider);
            kernel.Plugins.AddFromType<DataQueryActivity>(nameof(DataQueryActivity), serviceProvider);
            kernel.Plugins.AddFromType<ImageAnalysisActivity>(nameof(ImageAnalysisActivity), serviceProvider);
            kernel.Plugins.AddFromType<ClassificationActivity>(nameof(ClassificationActivity), serviceProvider);
            kernel.Plugins.AddFromType<ComparisonActivity>(nameof(ComparisonActivity), serviceProvider);
            kernel.Plugins.AddFromType<ContentAnalysisActivity>(nameof(ContentAnalysisActivity), serviceProvider);
            kernel.Plugins.AddFromType<FormBindingActivity>(nameof(FormBindingActivity), serviceProvider);
            kernel.Plugins.AddFromType<DocumentActivity>(nameof(DocumentActivity), serviceProvider);
            kernel.Plugins.AddFromType<PlanActivity>(nameof(PlanActivity), serviceProvider);
        }
    }
}
